<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="http">Presentation/Presentation.csproj</projectFile>
    <projectFile profileName="https">Presentation/Presentation.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0805aac3-a2db-438d-b9be-89e4e49c47d9" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 4
}]]></component>
  <component name="ProjectId" id="2xleFzECX6gWcUsKvIkaGIGdOEs" />
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.Presentation: https.executor": "Run",
    "Azure - Web App.Publish Presentation to Azure (1).executor": "Run",
    "Azure - Web App.Publish Presentation to Azure.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "ignore.virus.scanning.warn.message": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.fileTypes",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.Presentation: https">
    <configuration name="Publish Presentation to Azure (1)" type="AzureWebAppDeploy">
      <option name="appServicePlanName" value="ASP-ventixe-8122" />
      <option name="appServicePlanResourceGroupName" value="ventixe" />
      <option name="operatingSystem" value="Windows" />
      <option name="pricingSize" value="F1" />
      <option name="pricingTier" value="Free" />
      <option name="projectConfiguration" value="Release" />
      <option name="projectPlatform" value="Any CPU" />
      <option name="publishableProjectPath" value="$PROJECT_DIR$/Presentation/Presentation.csproj" />
      <option name="region" value="centralus" />
      <option name="resourceGroupName" value="ventixe" />
      <option name="subscriptionId" value="718d4082-d782-4bfb-8991-f3e7fd182d09" />
      <option name="webAppName" value="eventservice" />
      <method v="2" />
    </configuration>
    <configuration name="Publish Presentation to Azure" type="AzureWebAppDeploy">
      <option name="appServicePlanName" value="ASP-ventixe-8122" />
      <option name="appServicePlanResourceGroupName" value="ventixe" />
      <option name="operatingSystem" value="Windows" />
      <option name="pricingSize" value="F1" />
      <option name="pricingTier" value="Free" />
      <option name="projectConfiguration" value="Release" />
      <option name="projectPlatform" value="Any CPU" />
      <option name="publishableProjectPath" value="$PROJECT_DIR$/Presentation/Presentation.csproj" />
      <option name="region" value="centralus" />
      <option name="resourceGroupName" value="ventixe" />
      <option name="subscriptionId" value="718d4082-d782-4bfb-8991-f3e7fd182d09" />
      <option name="webAppName" value="eventservice" />
      <method v="2" />
    </configuration>
    <configuration name="Presentation: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Presentation/Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="Presentation: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Presentation/Presentation.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0805aac3-a2db-438d-b9be-89e4e49c47d9" name="Changes" comment="" />
      <created>1748516988397</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748516988397</updated>
      <workItem from="1748516989631" duration="1041000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>