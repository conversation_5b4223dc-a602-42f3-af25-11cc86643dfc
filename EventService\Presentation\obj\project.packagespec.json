﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\RiderProjects\\Ventixetest\\EventService\\Presentation\\Presentation.csproj","projectName":"Presentation","projectPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixetest\\EventService\\Presentation\\Presentation.csproj","outputPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixetest\\EventService\\Presentation\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\RiderProjects\\Ventixetest\\EventService\\Application\\Application.csproj":{"projectPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixetest\\EventService\\Application\\Application.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.200"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[9.0.4, )"},"Microsoft.EntityFrameworkCore.Design":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.5, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[8.1.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}