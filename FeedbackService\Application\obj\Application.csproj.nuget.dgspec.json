{"format": 1, "restore": {"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\Application.csproj": {}}, "projects": {"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\Application.csproj", "projectName": "Application", "projectPath": "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\Persistence.csproj": {"projectPath": "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\Persistence.csproj", "projectName": "Persistence", "projectPath": "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}