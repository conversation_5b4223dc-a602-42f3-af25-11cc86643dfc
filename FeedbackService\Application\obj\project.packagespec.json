﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\Application.csproj","projectName":"Application","projectPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\Application.csproj","outputPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Application\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\Persistence.csproj":{"projectPath":"C:\\Users\\<USER>\\RiderProjects\\Ventixe\\FeedbackService\\Persistence\\Persistence.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.200"}"frameworks":{"net9.0":{"targetAlias":"net9.0","imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}