# .NET Core build folders
bin/
obj/

# Visual Studio / JetBrains Rider / Visual Studio Code
.vs/
.vscode/
.idea/
*.user
*.userosscache
*.suo
*.userprefs
*.dbmdl
*.jfm
*.pfx
*.publishsettings
*.vscode/
*.DotSettings.user
.env

# Rider auto-generated files
.idea/
*.sln.iml

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/
msbuild.log
msbuild.err
msbuild.wrn

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!*.[Cc]ache/

# Local configuration
appsettings.Development.json
appsettings.local.json
appsettings.*.local.json

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Rider
.idea/

# User-specific files
*.rsuser

# Visual Studio IntelliCode
.vsconfig

# Test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Entity Framework
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings

# SQLite database files
*.db
*.db-shm
*.db-wal

# Backup & report files from converting an old project file to a newer
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm
