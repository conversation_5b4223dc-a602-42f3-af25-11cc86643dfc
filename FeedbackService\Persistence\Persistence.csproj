<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.2" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.2" />
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.2" />
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="9.0.2" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.2" />
    </ItemGroup>

</Project>
