{"name": "ventixe-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "formik": "^2.4.5", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.20.0", "recharts": "^2.15.3", "styled-components": "^6.1.1", "web-vitals": "^2.1.4", "yup": "^1.3.2"}, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "test": "vitest run", "test:watch": "vitest", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^6.3.5", "vitest": "^3.1.4"}}