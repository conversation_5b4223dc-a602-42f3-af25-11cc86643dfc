import { eventsApi } from './api';
import { ENDPOINTS } from '../config/apiConfig';

export const eventService = {
  // Get all events
  getAllEvents: async () => {
    const response = await eventsApi.get(ENDPOINTS.EVENTS.ALL);
    return response.data;
  },

  // Get event by ID
  getEventById: async (id) => {
    const response = await eventsApi.get(ENDPOINTS.EVENTS.BY_ID(id));
    return response.data;
  },

  // Create new event
  createEvent: async (eventData) => {
    const response = await eventsApi.post(ENDPOINTS.EVENTS.CREATE, eventData);
    return response.data;
  },

  // Update event
  updateEvent: async (id, eventData) => {
    const response = await eventsApi.put(ENDPOINTS.EVENTS.UPDATE(id), eventData);
    return response.data;
  },

  // Delete event
  deleteEvent: async (id) => {
    const response = await eventsApi.delete(ENDPOINTS.EVENTS.DELETE(id));
    return response.data;
  }
};